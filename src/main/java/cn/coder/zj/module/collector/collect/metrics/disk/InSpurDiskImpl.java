package cn.coder.zj.module.collector.collect.metrics.disk;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.service.apicache.FsApiCacheService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper.*;
import static cn.coder.zj.module.collector.enums.DiskType.PROTOCOL_IN_SPUR_DISK;
import static cn.coder.zj.module.collector.enums.MetricNameType.*;
import static cn.coder.zj.module.collector.util.ApiUtil.getInSpurJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class InSpurDiskImpl extends AbstractMetrics implements MetricsCollectHelper.MetricsHandler {
    protected long startTime;

    @Override
    public void preCheck(Platform platform) {

    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                List<MetricData> metricDataList = collectData(platform);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.DISK_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("collect basic data end, cost {} seconds", endTimeFormatted);
            });
        }
    }

    private List<MetricData> collectData(Platform platformObj) {
        List<MetricData> list = new ArrayList<>();
        list.addAll(collectInSpurVmData(platformObj, "DISK", this));
        list.addAll(collectInSpurHostData(platformObj, "DISK", this));
        list.addAll(collectInSpurStorageData(platformObj));
        return list;
    }

    protected List<MetricData> collectInSpurStorageData(Platform platform) {
        String platformUrl = platform.getPlatformUrl();
        String token = platform.getInSpurPlatform().getToken();
        if (token == null) {
            log.error("平台 {} token为空", platform.getPlatformName());
            return new ArrayList<>();
        }

        // 构建请求头
        Map<String, String> header = Map.of(
                "version", "5.8",
                "Content-Type", "application/json",
                "Authorization", token
        );

        JsonObject vmdata = FsApiCacheService.getJsonObject(platformUrl + InSpurApiConstant.DATASTORES_LIST, null, header);
        JsonArray vmArray = vmdata.getAsJsonArray("items");
        if (ObjectUtil.isNull(vmArray)) return new ArrayList<>();

        List<MetricData> metricDataList = new ArrayList<>();
        for (JsonElement element : vmArray) {
            JsonObject asJsonObject = element.getAsJsonObject();
            MetricData metricData = createData(platform, asJsonObject, DISK_USED_TASK.code(),"storage");
            BigDecimal capacity = getBigFromJson(asJsonObject, "capacity");
            BigDecimal usedCapacity = getBigFromJson(asJsonObject, "usedCapacity");
            BigDecimal divide = usedCapacity.divide(capacity).multiply(new BigDecimal(100)).setScale(0, RoundingMode.DOWN);
            metricData.setValues(Arrays.asList(divide.doubleValue()));
            metricDataList.add(metricData);
        }
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_IN_SPUR_DISK.code();
    }

    @Override
    public List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();

        String id = getStringFromJson(cloud, "id");
        Map<String, String> params = Map.of(
                "targetType", "VM",
                "type", "0",
                "itemIns", "storage_kbps",
                "objuuid", id
        );

        JsonArray valueArrayKbs = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, headers);

        Map<String, String> params1 = Map.of(
                "targetType", "VM",
                "type", "0",
                "itemIns", "storage_iops",
                "objuuid", id
        );

        JsonArray valueArrayPbs = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params1, headers);

        MetricData metricData = createData(platform, cloud, DISK_READ_TASK.code(),"vm");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArrayKbs) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("storage_kbps-read".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data").multiply(new BigDecimal(1024));
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("vm");
                    metricDataList.add(metricData);
                }
            }

            if ("storage_kbps-write".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data").multiply(new BigDecimal(1024));
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_TASK.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("vm");
                    metricDataList.add(data);
                }
            }
        }

        for (JsonElement element : valueArrayPbs) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("storage_iops-read".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_READ_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("vm");
                    metricDataList.add(data);
                }
            }

            if ("storage_iops-write".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("vm");
                    metricDataList.add(data);
                }
            }
        }

        BigDecimal memoryUsage = getBigFromJson(cloud, "memoryUsage");
        MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
        data.setMetricName(DISK_USED_TASK.code());
        data.setValues(Arrays.asList(memoryUsage.doubleValue()));
        metricDataList.add(data);

        return metricDataList;
    }

    @Override
    public List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject host = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String id = getStringFromJson(host, "id");
        Map<String, String> params = Map.of(
                "targetType", "HOST",
                "type", "0",
                "itemIns", "storage_kbps",
                "objuuid", id
        );

        JsonArray valueArrayKbs = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, headers);

        Map<String, String> params1 = Map.of(
                "targetType", "HOST",
                "type", "0",
                "itemIns", "storage_iops",
                "objuuid", id
        );

        JsonArray valueArrayPbs = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params1, headers);

        MetricData metricData = createData(platform, host, DISK_READ_TASK.code(),"host");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArrayKbs) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("storage_kbps-read".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data").multiply(new BigDecimal(1024));
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("host");
                    metricDataList.add(metricData);
                }
            }

            if ("storage_kbps-write".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data").multiply(new BigDecimal(1024));
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_TASK.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("host");
                    metricDataList.add(data);
                }
            }
        }

        for (JsonElement element : valueArrayPbs) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("storage_iops-read".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_READ_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("host");
                    metricDataList.add(data);
                }
            }

            if ("storage_iops-write".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data");
                    MetricData data = BeanUtil.copyProperties(metricData, MetricData.class);
                    data.setMetricName(DISK_WRITE_OPS.code());
                    data.setValues(Arrays.asList(metricValue.doubleValue()));
                    data.setType("host");
                    metricDataList.add(data);
                }
            }
        }

        return metricDataList;
    }
}
