package cn.coder.zj.module.collector.collect.metrics.cpu;

import cn.coder.zj.module.collector.collect.metrics.AbstractMetrics;
import cn.coder.zj.module.collector.collect.metrics.MetricsCollectHelper;
import cn.coder.zj.module.collector.common.cache.CacheService;
import cn.coder.zj.module.collector.constants.inspur.InSpurApiConstant;
import cn.coder.zj.module.collector.dal.manager.MetricData;
import cn.coder.zj.module.collector.dal.platform.Platform;
import cn.coder.zj.module.collector.enums.PlatformType;
import cn.coder.zj.module.collector.remoting.send.SendMessageService;
import cn.coder.zj.module.collector.util.SpringBeanUtils;
import cn.iocoder.zj.framework.common.message.ClusterMsg;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.coder.zj.module.collector.enums.CpuType.PROTOCOL_IN_SPUR_CPU;
import static cn.coder.zj.module.collector.enums.MetricNameType.CPU_USED_TASK;
import static cn.coder.zj.module.collector.util.ApiUtil.getInSpurJsonArrayFromApi;
import static cn.coder.zj.module.collector.util.CommonUtil.getBigFromJson;
import static cn.coder.zj.module.collector.util.CommonUtil.getStringFromJson;
import static cn.coder.zj.module.collector.util.GsonUtil.GSON;

@Slf4j
public class InSpurCpuImpl extends AbstractMetrics implements MetricsCollectHelper.MetricsHandler {
    protected long startTime;
    
    @Override
    public void preCheck(Platform platform) {
        // 预检查逻辑
    }

    @Override
    public void collectData(ClusterMsg.Message.Builder message) {
        this.startTime = System.currentTimeMillis();
        List<Object> platformList = CacheService.getValuesByKey1(PlatformType.IN_SPUR.code());
        TaskExecutor taskExecutor = SpringBeanUtils.getBean(TaskExecutor.class);
        SendMessageService sendMessageService = SpringBeanUtils.getBean(SendMessageService.class);

        for (Object platformObj : platformList) {
            taskExecutor.execute(() -> {
                Platform platform = (Platform) platformObj;
                List<MetricData> metricDataList = collectData(platform);

                message.setData(new Gson().toJson(metricDataList));
                message.setTime(System.currentTimeMillis());
                message.setType(ClusterMsg.MessageType.CPU_TASK);
                sendMessageService.sendMessage(CacheService.getCtx("ctx"), message.build());
                String endTimeFormatted = String.format("%.2f", (System.currentTimeMillis() - startTime) / 1000.0);
                log.info("浪潮-CPU性能采集 {} 秒,{} 平台", endTimeFormatted,platform.getPlatformName());
            });
        }
    }

    private List<MetricData> collectData(Platform platform) {
        List<MetricData> list = new ArrayList<>();
        list.addAll(MetricsCollectHelper.collectInSpurVmData(platform, "CPU", this));
        list.addAll(MetricsCollectHelper.collectInSpurHostData(platform, "CPU", this));
        return list;
    }

    @Override
    public List<MetricData> handleVmMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject cloud = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String id = getStringFromJson(cloud, "id");
        Map<String, String> params = Map.of(
                "targetType", "VM",
                "type", "0",
                "itemIns", "CPU",
                "objuuid", id
        );

        JsonArray valueArray = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, headers);
        MetricData metricData = MetricsCollectHelper.createData(platform, cloud, CPU_USED_TASK.code(),"vm");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("cpu".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data");
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("vm");
                    metricDataList.add(metricData);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public List<MetricData> handleHostMetrics(Platform platform, JsonElement jsonElement, Map<String, String> headers) {
        String platformUrl = platform.getPlatformUrl();
        JsonObject host = GSON.toJsonTree(jsonElement).getAsJsonObject();
        String id = getStringFromJson(host, "id");
        Map<String, String> params = Map.of(
                "targetType", "HOST",
                "type", "0",
                "itemIns", "CPU",
                "objuuid", id
        );

        JsonArray valueArray = getInSpurJsonArrayFromApi(platformUrl + InSpurApiConstant.GET_REALTIME_DATA, params, headers);
        MetricData metricData = MetricsCollectHelper.createData(platform, host, CPU_USED_TASK.code(),"host");
        List<MetricData> metricDataList = new ArrayList<>();

        for (JsonElement element : valueArray) {
            JsonObject asJsonObject = element.getAsJsonObject();
            String key = getStringFromJson(asJsonObject, "key");
            JsonArray perfPoints = asJsonObject.getAsJsonArray("perfPoints");
            if ("cpu".equals(key)) {
                if (perfPoints != null && perfPoints.size() > 0) {
                    JsonObject lastPoint = perfPoints.get(perfPoints.size() - 1).getAsJsonObject();
                    BigDecimal metricValue = getBigFromJson(lastPoint, "data");
                    metricData.setValues(Arrays.asList(metricValue.doubleValue()));
                    metricData.setType("host");
                    metricDataList.add(metricData);
                }
            }
        }
        return metricDataList;
    }

    @Override
    public String supportProtocol() {
        return PROTOCOL_IN_SPUR_CPU.code();
    }
}
